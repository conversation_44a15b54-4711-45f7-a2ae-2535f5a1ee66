<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ViralChef</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      width: 350px;
      min-height: 400px;
      background: linear-gradient(135deg, #1a1a1a, #2d1b1b);
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      color: #ffffff;
      overflow-x: hidden;
      margin: 0;
      padding: 0;
    }
    
    .container {
      padding: 20px;
      backdrop-filter: blur(10px);
      min-height: 400px;
      animation: slideInFromLeft 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      position: relative;
    }

    /* Animation for sliding popup from left to right across browser width */
    @keyframes slideInFromLeft {
      0% {
        transform: translateX(-100vw);
        opacity: 0;
      }
      70% {
        opacity: 0.8;
      }
      100% {
        transform: translateX(0);
        opacity: 1;
      }
    }
    
    .header {
      text-align: center;
      margin-bottom: 24px;
    }
    
    .logo {
      width: 48px;
      height: 48px;
      margin: 0 auto 12px;
      background: linear-gradient(135deg, #dc2626, #7f1d1d);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;
      font-weight: bold;
      box-shadow: 0 4px 15px rgba(220, 38, 38, 0.4);
      border: 1px solid rgba(220, 38, 38, 0.3);
    }
    
    .title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 4px;
      color: #ffffff;
    }

    .subtitle {
      font-size: 12px;
      color: #9ca3af;
    }
    
    .glass-card {
      background: rgba(0, 0, 0, 0.4);
      backdrop-filter: blur(15px);
      border-radius: 16px;
      border: 1px solid rgba(220, 38, 38, 0.2);
      padding: 16px;
      margin-bottom: 16px;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
      transition: all 0.3s ease;
    }

    .glass-card:hover {
      border-color: rgba(220, 38, 38, 0.4);
      box-shadow: 0 12px 35px rgba(220, 38, 38, 0.1);
    }
    
    .input-group {
      margin-bottom: 16px;
    }
    
    .input-group label {
      display: block;
      font-size: 12px;
      font-weight: 500;
      margin-bottom: 6px;
      color: #e5e7eb;
    }
    
    .input-group input {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid rgba(220, 38, 38, 0.3);
      border-radius: 8px;
      background: rgba(0, 0, 0, 0.3);
      backdrop-filter: blur(5px);
      font-size: 14px;
      color: #ffffff;
      transition: all 0.2s ease;
    }

    .input-group input::placeholder {
      color: #9ca3af;
    }

    .input-group input:focus {
      outline: none;
      border-color: #dc2626;
      box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.2);
      background: rgba(0, 0, 0, 0.5);
    }
    
    .glass-button {
      width: 100%;
      padding: 12px;
      background: rgba(220, 38, 38, 0.8);
      border: 1px solid rgba(220, 38, 38, 0.4);
      border-radius: 12px;
      color: white;
      font-weight: 500;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      margin-bottom: 8px;
      box-shadow: 0 4px 15px rgba(220, 38, 38, 0.2);
    }
    
    .glass-button:hover {
      background: rgba(220, 38, 38, 0.9);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
      border-color: rgba(220, 38, 38, 0.6);
    }

    .glass-button:active {
      transform: translateY(0);
      box-shadow: 0 4px 15px rgba(220, 38, 38, 0.2);
    }

    .glass-button.secondary {
      background: rgba(75, 85, 99, 0.8);
      border-color: rgba(75, 85, 99, 0.4);
      box-shadow: 0 4px 15px rgba(75, 85, 99, 0.2);
    }

    .glass-button.secondary:hover {
      background: rgba(75, 85, 99, 0.9);
      box-shadow: 0 8px 25px rgba(75, 85, 99, 0.3);
    }
    
    .quick-actions {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
      margin-top: 16px;
    }
    
    .quick-action {
      padding: 8px;
      background: rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(220, 38, 38, 0.3);
      border-radius: 8px;
      text-align: center;
      font-size: 11px;
      color: #ffffff;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(5px);
    }

    .quick-action:hover {
      background: rgba(220, 38, 38, 0.2);
      border-color: rgba(220, 38, 38, 0.5);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(220, 38, 38, 0.2);
    }
    
    .status {
      text-align: center;
      font-size: 12px;
      color: #9ca3af;
      margin-top: 16px;
    }
    
    .loading {
      display: none;
      text-align: center;
      padding: 20px;
    }
    
    .spinner {
      width: 24px;
      height: 24px;
      border: 2px solid rgba(220, 38, 38, 0.3);
      border-top: 2px solid #dc2626;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 8px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">🍳</div>
      <div class="title">ViralChef</div>
      <div class="subtitle">AI Recipe Generator</div>
    </div>
    
    <div class="glass-card">
      <div class="input-group">
        <label for="recipe-title">Recipe Title</label>
        <input type="text" id="recipe-title" placeholder="e.g., Spicy Tuna Pasta">
      </div>
      <button class="glass-button" id="generate-btn">
        🤖 Generate Ingredients
      </button>
    </div>
    
    <div class="glass-card">
      <div class="input-group">
        <label for="search-query">Search Viral Recipes</label>
        <input type="text" id="search-query" placeholder="e.g., trending pasta recipes">
      </div>
      <button class="glass-button secondary" id="search-btn">
        🔥 Find Viral Recipes
      </button>
    </div>
    
    <button class="glass-button" id="dashboard-btn">
      📊 Open Dashboard
    </button>
    
    <div class="quick-actions">
      <div class="quick-action" id="saved-recipes">
        📚 Saved<br>Recipes
      </div>
      <div class="quick-action" id="settings">
        ⚙️ Settings
      </div>
      <div class="quick-action" id="trending">
        📈 Trending<br>Now
      </div>
      <div class="quick-action" id="favorites">
        ❤️ Favorites
      </div>
    </div>
    
    <div class="loading" id="loading">
      <div class="spinner"></div>
      <div>Processing...</div>
    </div>
    
    <div class="status" id="status">
      Ready to generate amazing recipes!
    </div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
