@echo off
echo Building ViralChef Extension...

REM Create dist directory
if not exist "dist" mkdir dist

REM Copy static files
copy manifest.json dist\
copy background.js dist\
copy content.js dist\
copy popup.html dist\
copy popup.js dist\
xcopy /E /I assets dist\assets

REM Copy dashboard files (for manual setup)
if not exist "dist\dashboard" mkdir dist\dashboard
copy dashboard\index.html dist\dashboard\

echo Build complete! 
echo.
echo To load the extension:
echo 1. Open Chrome and go to chrome://extensions/
echo 2. Enable Developer mode
echo 3. Click "Load unpacked" and select the 'dist' folder
echo.
echo Note: For full functionality, you'll need to:
echo 1. Set up a proper build process with Vite
echo 2. Install and configure the React components
echo 3. Add your API keys in the extension settings
echo.
pause
