<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ViralChef</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      width: 350px;
      min-height: 400px;
      background: linear-gradient(135deg, #10b981, #f8fafc);
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      color: #1f2937;
      overflow-x: hidden;
    }
    
    .container {
      padding: 20px;
      backdrop-filter: blur(10px);
      min-height: 400px;
      animation: slideInFromLeft 0.6s ease-out;
    }

    /* Animation for sliding from left to right */
    @keyframes slideInFromLeft {
      0% {
        transform: translateX(-100%);
        opacity: 0;
      }
      100% {
        transform: translateX(0);
        opacity: 1;
      }
    }
    
    .header {
      text-align: center;
      margin-bottom: 24px;
    }
    
    .logo {
      width: 48px;
      height: 48px;
      margin: 0 auto 12px;
      background: #10b981;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;
      font-weight: bold;
      box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
      animation: slideInFromLeft 0.8s ease-out 0.2s both;
    }
    
    .title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 4px;
    }
    
    .subtitle {
      font-size: 12px;
      color: #6b7280;
    }
    
    .glass-card {
      background: rgba(255, 255, 255, 0.25);
      backdrop-filter: blur(15px);
      border-radius: 16px;
      border: 1px solid rgba(255, 255, 255, 0.4);
      padding: 16px;
      margin-bottom: 16px;
      box-shadow: 0 8px 25px rgba(16, 185, 129, 0.1);
      animation: slideInFromLeft 0.7s ease-out 0.3s both;
      transition: all 0.3s ease;
    }

    .glass-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 12px 35px rgba(16, 185, 129, 0.15);
    }
    
    .input-group {
      margin-bottom: 16px;
    }
    
    .input-group label {
      display: block;
      font-size: 12px;
      font-weight: 500;
      margin-bottom: 6px;
      color: #374151;
    }
    
    .input-group input {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.5);
      backdrop-filter: blur(5px);
      font-size: 14px;
      transition: all 0.2s ease;
    }
    
    .input-group input:focus {
      outline: none;
      border-color: #10b981;
      box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    }
    
    .glass-button {
      width: 100%;
      padding: 12px;
      background: linear-gradient(135deg, rgba(16, 185, 129, 0.8), rgba(59, 130, 246, 0.8));
      border: none;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s ease;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      margin-bottom: 8px;
    }
    
    .glass-button:hover {
      transform: translateY(-1px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }
    
    .glass-button:active {
      transform: translateY(0);
    }
    
    .glass-button.secondary {
      background: linear-gradient(135deg, rgba(107, 114, 128, 0.8), rgba(75, 85, 99, 0.8));
    }
    
    .quick-actions {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
      margin-top: 16px;
    }
    
    .quick-action {
      padding: 8px;
      background: rgba(255, 255, 255, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.4);
      border-radius: 6px;
      text-align: center;
      font-size: 11px;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .quick-action:hover {
      background: rgba(255, 255, 255, 0.4);
      transform: translateY(-1px);
    }
    
    .status {
      text-align: center;
      font-size: 12px;
      color: #6b7280;
      margin-top: 16px;
    }
    
    .loading {
      display: none;
      text-align: center;
      padding: 20px;
    }
    
    .spinner {
      width: 24px;
      height: 24px;
      border: 2px solid rgba(16, 185, 129, 0.3);
      border-top: 2px solid #10b981;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 8px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">🍳</div>
      <div class="title">ViralChef</div>
      <div class="subtitle">AI Recipe Generator</div>
    </div>
    
    <div class="glass-card">
      <div class="input-group">
        <label for="recipe-title">Recipe Title</label>
        <input type="text" id="recipe-title" placeholder="e.g., Spicy Tuna Pasta">
      </div>
      <button class="glass-button" id="generate-btn">
        🤖 Generate Ingredients
      </button>
    </div>
    
    <div class="glass-card">
      <div class="input-group">
        <label for="search-query">Search Viral Recipes</label>
        <input type="text" id="search-query" placeholder="e.g., trending pasta recipes">
      </div>
      <button class="glass-button secondary" id="search-btn">
        🔥 Find Viral Recipes
      </button>
    </div>
    
    <button class="glass-button" id="dashboard-btn">
      📊 Open Dashboard
    </button>
    
    <div class="quick-actions">
      <div class="quick-action" id="saved-recipes">
        📚 Saved<br>Recipes
      </div>
      <div class="quick-action" id="settings">
        ⚙️ Settings
      </div>
      <div class="quick-action" id="trending">
        📈 Trending<br>Now
      </div>
      <div class="quick-action" id="favorites">
        ❤️ Favorites
      </div>
    </div>
    
    <div class="loading" id="loading">
      <div class="spinner"></div>
      <div>Processing...</div>
    </div>
    
    <div class="status" id="status">
      Ready to generate amazing recipes!
    </div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
