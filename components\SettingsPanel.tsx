import React, { useState, useEffect } from 'react'
import { Setting<PERSON>, <PERSON>, Globe, Palette, Shield, Save, Eye, EyeOff } from 'lucide-react'
import { StorageManager, UserSettings, ValidationUtils } from '../utils/helpers'
import GlassButton from './GlassButton'

const SettingsPanel: React.FC = () => {
  const [settings, setSettings] = useState<UserSettings | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [showApiKeys, setShowApiKeys] = useState({
    openai: false,
    deepseek: false,
    google: false,
    facebook: false
  })
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      setIsLoading(true)
      const userSettings = await StorageManager.getSettings()
      setSettings(userSettings)
    } catch (error) {
      console.error('Failed to load settings:', error)
      setMessage({ type: 'error', text: 'Failed to load settings' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveSettings = async () => {
    if (!settings) return

    try {
      setIsSaving(true)
      await StorageManager.saveSettings(settings)
      setMessage({ type: 'success', text: 'Settings saved successfully!' })
      
      // Clear message after 3 seconds
      setTimeout(() => setMessage(null), 3000)
    } catch (error) {
      console.error('Failed to save settings:', error)
      setMessage({ type: 'error', text: 'Failed to save settings' })
    } finally {
      setIsSaving(false)
    }
  }

  const updateSettings = (updates: Partial<UserSettings>) => {
    if (settings) {
      setSettings({ ...settings, ...updates })
    }
  }

  const updateApiKey = (provider: keyof UserSettings['apiKeys'], value: string) => {
    if (settings) {
      setSettings({
        ...settings,
        apiKeys: {
          ...settings.apiKeys,
          [provider]: value
        }
      })
    }
  }

  const validateApiKey = (provider: string, key: string): boolean => {
    if (provider === 'openai' || provider === 'deepseek') {
      return ValidationUtils.validateApiKey(provider as 'openai' | 'deepseek', key)
    }
    return key.length > 0
  }

  const toggleApiKeyVisibility = (provider: keyof typeof showApiKeys) => {
    setShowApiKeys(prev => ({
      ...prev,
      [provider]: !prev[provider]
    }))
  }

  if (isLoading) {
    return (
      <div className="glass-card p-8 text-center">
        <div className="loading-spinner mx-auto mb-4"></div>
        <p className="text-gray-600">Loading settings...</p>
      </div>
    )
  }

  if (!settings) {
    return (
      <div className="glass-card p-8 text-center">
        <p className="text-red-600">Failed to load settings</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center space-x-3 mb-4">
          <div className="w-12 h-12 bg-gradient-to-r from-gray-500 to-gray-700 rounded-xl flex items-center justify-center animate-float">
            <Settings className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold gradient-text">Settings</h1>
            <p className="text-gray-600">Configure your ViralChef experience</p>
          </div>
        </div>
      </div>

      {/* Message */}
      {message && (
        <div className={`p-4 rounded-lg ${
          message.type === 'success' 
            ? 'bg-green-100/50 border border-green-200/50 text-green-700'
            : 'bg-red-100/50 border border-red-200/50 text-red-700'
        }`}>
          {message.text}
        </div>
      )}

      {/* General Settings */}
      <div className="glass-card p-6">
        <div className="flex items-center space-x-2 mb-4">
          <Palette className="w-5 h-5 text-purple-600" />
          <h2 className="text-xl font-semibold text-gray-800">General</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Language</label>
            <select
              value={settings.language}
              onChange={(e) => updateSettings({ language: e.target.value as any })}
              className="glass-input w-full"
            >
              <option value="en">English</option>
              <option value="ar">العربية (Arabic)</option>
              <option value="fr">Français (French)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Theme</label>
            <select
              value={settings.theme}
              onChange={(e) => updateSettings({ theme: e.target.value as any })}
              className="glass-input w-full"
            >
              <option value="auto">Auto</option>
              <option value="light">Light</option>
              <option value="dark">Dark</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">AI Provider</label>
            <select
              value={settings.aiProvider}
              onChange={(e) => updateSettings({ aiProvider: e.target.value as any })}
              className="glass-input w-full"
            >
              <option value="openai">OpenAI GPT</option>
              <option value="deepseek">DeepSeek</option>
            </select>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="notifications"
              checked={settings.notifications}
              onChange={(e) => updateSettings({ notifications: e.target.checked })}
              className="w-4 h-4 text-emerald-600 rounded focus:ring-emerald-500"
            />
            <label htmlFor="notifications" className="text-sm font-medium text-gray-700">
              Enable notifications
            </label>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="autoSave"
              checked={settings.autoSave}
              onChange={(e) => updateSettings({ autoSave: e.target.checked })}
              className="w-4 h-4 text-emerald-600 rounded focus:ring-emerald-500"
            />
            <label htmlFor="autoSave" className="text-sm font-medium text-gray-700">
              Auto-save generated recipes
            </label>
          </div>
        </div>
      </div>

      {/* API Keys */}
      <div className="glass-card p-6">
        <div className="flex items-center space-x-2 mb-4">
          <Key className="w-5 h-5 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-800">API Keys</h2>
        </div>

        <div className="space-y-4">
          {/* OpenAI API Key */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              OpenAI API Key
              <span className="text-xs text-gray-500 ml-2">(Required for AI recipe generation)</span>
            </label>
            <div className="relative">
              <input
                type={showApiKeys.openai ? 'text' : 'password'}
                value={settings.apiKeys.openai || ''}
                onChange={(e) => updateApiKey('openai', e.target.value)}
                placeholder="sk-..."
                className={`glass-input w-full pr-10 ${
                  settings.apiKeys.openai && !validateApiKey('openai', settings.apiKeys.openai)
                    ? 'border-red-300 focus:ring-red-500'
                    : ''
                }`}
              />
              <button
                type="button"
                onClick={() => toggleApiKeyVisibility('openai')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                {showApiKeys.openai ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
            {settings.apiKeys.openai && !validateApiKey('openai', settings.apiKeys.openai) && (
              <p className="text-red-600 text-xs mt-1">Invalid OpenAI API key format</p>
            )}
          </div>

          {/* DeepSeek API Key */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              DeepSeek API Key
              <span className="text-xs text-gray-500 ml-2">(Alternative AI provider)</span>
            </label>
            <div className="relative">
              <input
                type={showApiKeys.deepseek ? 'text' : 'password'}
                value={settings.apiKeys.deepseek || ''}
                onChange={(e) => updateApiKey('deepseek', e.target.value)}
                placeholder="Enter DeepSeek API key"
                className="glass-input w-full pr-10"
              />
              <button
                type="button"
                onClick={() => toggleApiKeyVisibility('deepseek')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                {showApiKeys.deepseek ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
          </div>

          {/* Google API Key */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Google Custom Search API Key
              <span className="text-xs text-gray-500 ml-2">(For Google recipe search)</span>
            </label>
            <div className="relative">
              <input
                type={showApiKeys.google ? 'text' : 'password'}
                value={settings.apiKeys.googleApiKey || ''}
                onChange={(e) => updateApiKey('googleApiKey', e.target.value)}
                placeholder="Enter Google API key"
                className="glass-input w-full pr-10"
              />
              <button
                type="button"
                onClick={() => toggleApiKeyVisibility('google')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                {showApiKeys.google ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
          </div>

          {/* Google Search Engine ID */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Google Search Engine ID
              <span className="text-xs text-gray-500 ml-2">(Custom Search Engine ID)</span>
            </label>
            <input
              type="text"
              value={settings.apiKeys.googleSearchEngineId || ''}
              onChange={(e) => updateApiKey('googleSearchEngineId', e.target.value)}
              placeholder="Enter Search Engine ID"
              className="glass-input w-full"
            />
          </div>

          {/* Facebook Access Token */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Facebook Access Token
              <span className="text-xs text-gray-500 ml-2">(Optional - for Facebook group search)</span>
            </label>
            <div className="relative">
              <input
                type={showApiKeys.facebook ? 'text' : 'password'}
                value={settings.apiKeys.facebookAccessToken || ''}
                onChange={(e) => updateApiKey('facebookAccessToken', e.target.value)}
                placeholder="Enter Facebook access token"
                className="glass-input w-full pr-10"
              />
              <button
                type="button"
                onClick={() => toggleApiKeyVisibility('facebook')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                {showApiKeys.facebook ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
          </div>
        </div>

        <div className="mt-4 p-3 bg-blue-100/50 border border-blue-200/50 rounded-lg">
          <p className="text-blue-700 text-sm">
            <Shield className="w-4 h-4 inline mr-1" />
            Your API keys are stored locally and never shared. They are only used to make requests to the respective services.
          </p>
        </div>
      </div>

      {/* Search Preferences */}
      <div className="glass-card p-6">
        <div className="flex items-center space-x-2 mb-4">
          <Globe className="w-5 h-5 text-green-600" />
          <h2 className="text-xl font-semibold text-gray-800">Search Preferences</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Max Results</label>
            <select
              value={settings.searchPreferences.maxResults}
              onChange={(e) => updateSettings({
                searchPreferences: {
                  ...settings.searchPreferences,
                  maxResults: parseInt(e.target.value)
                }
              })}
              className="glass-input w-full"
            >
              <option value={10}>10 results</option>
              <option value={20}>20 results</option>
              <option value={50}>50 results</option>
              <option value={100}>100 results</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Min Engagement</label>
            <select
              value={settings.searchPreferences.minEngagement}
              onChange={(e) => updateSettings({
                searchPreferences: {
                  ...settings.searchPreferences,
                  minEngagement: parseInt(e.target.value)
                }
              })}
              className="glass-input w-full"
            >
              <option value={10}>10+ interactions</option>
              <option value={50}>50+ interactions</option>
              <option value={100}>100+ interactions</option>
              <option value={500}>500+ interactions</option>
              <option value={1000}>1000+ interactions</option>
            </select>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="includeFacebook"
              checked={settings.searchPreferences.includeFacebook}
              onChange={(e) => updateSettings({
                searchPreferences: {
                  ...settings.searchPreferences,
                  includeFacebook: e.target.checked
                }
              })}
              className="w-4 h-4 text-emerald-600 rounded focus:ring-emerald-500"
            />
            <label htmlFor="includeFacebook" className="text-sm font-medium text-gray-700">
              Include Facebook search
            </label>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="includeGoogle"
              checked={settings.searchPreferences.includeGoogle}
              onChange={(e) => updateSettings({
                searchPreferences: {
                  ...settings.searchPreferences,
                  includeGoogle: e.target.checked
                }
              })}
              className="w-4 h-4 text-emerald-600 rounded focus:ring-emerald-500"
            />
            <label htmlFor="includeGoogle" className="text-sm font-medium text-gray-700">
              Include Google search
            </label>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-center">
        <GlassButton
          onClick={handleSaveSettings}
          loading={isSaving}
          icon={Save}
          size="lg"
        >
          {isSaving ? 'Saving...' : 'Save Settings'}
        </GlassButton>
      </div>

      {/* Help Section */}
      <div className="glass-card p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-3">Need Help?</h3>
        <div className="space-y-2 text-sm text-gray-600">
          <p>• <strong>OpenAI API Key:</strong> Get yours at <a href="https://platform.openai.com/api-keys" target="_blank" className="text-blue-600 hover:underline">platform.openai.com</a></p>
          <p>• <strong>Google Custom Search:</strong> Set up at <a href="https://developers.google.com/custom-search" target="_blank" className="text-blue-600 hover:underline">Google Custom Search</a></p>
          <p>• <strong>Facebook Access Token:</strong> Create an app at <a href="https://developers.facebook.com" target="_blank" className="text-blue-600 hover:underline">Facebook Developers</a></p>
          <p>• <strong>DeepSeek API:</strong> Alternative AI provider with competitive pricing</p>
        </div>
      </div>
    </div>
  )
}

export default SettingsPanel
