// ViralChef Extension Background Script
console.log('ViralChef Extension Background Script Loaded');

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
  console.log('ViralChef Extension installed:', details.reason);
  
  // Set default settings
  chrome.storage.sync.set({
    language: 'en',
    aiProvider: 'openai',
    searchPreferences: {
      includeFacebook: true,
      includeGoogle: true,
      maxResults: 10
    }
  });
});

// Handle messages from content scripts and popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Background received message:', request);
  
  switch (request.action) {
    case 'openDashboard':
      openDashboard();
      break;
    
    case 'generateRecipe':
      handleRecipeGeneration(request.data, sendResponse);
      return true; // Keep message channel open for async response
    
    case 'searchViralRecipes':
      handleViralSearch(request.data, sendResponse);
      return true; // Keep message channel open for async response
    
    case 'saveRecipe':
      saveRecipe(request.data, sendResponse);
      break;
    
    default:
      console.log('Unknown action:', request.action);
  }
});

// Open dashboard in new tab
function openDashboard() {
  chrome.tabs.create({
    url: chrome.runtime.getURL('dashboard/index.html')
  });
}

// Handle recipe generation requests
async function handleRecipeGeneration(data, sendResponse) {
  try {
    console.log('Generating recipe for:', data.title);
    
    // Get user settings
    const settings = await chrome.storage.sync.get(['aiProvider', 'language']);
    
    // This will be implemented when we create the AI utility
    const recipe = await generateRecipeIngredients(data.title, settings);
    
    sendResponse({ success: true, recipe });
  } catch (error) {
    console.error('Recipe generation failed:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// Handle viral recipe search requests
async function handleViralSearch(data, sendResponse) {
  try {
    console.log('Searching viral recipes for:', data.query);
    
    // Get user settings
    const settings = await chrome.storage.sync.get(['searchPreferences']);
    
    // This will be implemented when we create the search utilities
    const results = await searchViralRecipes(data.query, settings.searchPreferences);
    
    sendResponse({ success: true, results });
  } catch (error) {
    console.error('Viral search failed:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// Save recipe to storage
async function saveRecipe(data, sendResponse) {
  try {
    const { recipes = [] } = await chrome.storage.local.get(['recipes']);
    
    const newRecipe = {
      id: Date.now().toString(),
      title: data.title,
      ingredients: data.ingredients,
      source: data.source || 'generated',
      createdAt: new Date().toISOString(),
      tags: data.tags || []
    };
    
    recipes.push(newRecipe);
    await chrome.storage.local.set({ recipes });
    
    sendResponse({ success: true, recipe: newRecipe });
  } catch (error) {
    console.error('Failed to save recipe:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// Placeholder functions - will be implemented with actual utilities
async function generateRecipeIngredients(title, settings) {
  // This will call the AI utility when implemented
  return {
    title,
    ingredients: ['Placeholder ingredient 1', 'Placeholder ingredient 2'],
    notes: 'Generated by AI'
  };
}

async function searchViralRecipes(query, preferences) {
  // This will call the search utilities when implemented
  return [
    {
      title: 'Sample Viral Recipe',
      source: 'facebook',
      engagement: { likes: 1000, shares: 500 },
      url: 'https://example.com'
    }
  ];
}
