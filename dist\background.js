// ViralChef Extension Background Script
console.log('ViralChef Extension Background Script Loaded');

// Handle action button click to open sidebar
chrome.action.onClicked.addListener((tab) => {
  openSidebar(tab.id);
});

// Function to open sidebar
async function openSidebar(tabId) {
  try {
    // Inject the sidebar into the current tab
    await chrome.scripting.executeScript({
      target: { tabId: tabId },
      func: createSidebar
    });
  } catch (error) {
    console.error('Failed to inject sidebar:', error);
    // Fallback: open dashboard in new tab
    openDashboard();
  }
}

// Function to create sidebar (injected into page)
function createSidebar() {
  // Remove existing sidebar if any
  const existingSidebar = document.getElementById('viralchef-sidebar');
  if (existingSidebar) {
    existingSidebar.remove();
    return;
  }

  // Create sidebar iframe
  const sidebar = document.createElement('iframe');
  sidebar.id = 'viralchef-sidebar';
  sidebar.src = chrome.runtime.getURL('popup.html');
  sidebar.style.cssText = `
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 400px !important;
    height: 100vh !important;
    border: none !important;
    z-index: 2147483647 !important;
    background: transparent !important;
    box-shadow: 5px 0 25px rgba(0, 0, 0, 0.4) !important;
  `;

  // Create overlay
  const overlay = document.createElement('div');
  overlay.id = 'viralchef-overlay';
  overlay.style.cssText = `
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: rgba(0, 0, 0, 0.5) !important;
    backdrop-filter: blur(5px) !important;
    z-index: 2147483646 !important;
    animation: fadeIn 0.3s ease-out !important;
  `;

  // Add animations
  const style = document.createElement('style');
  style.textContent = `
    @keyframes fadeIn {
      0% { opacity: 0; }
      100% { opacity: 1; }
    }
    @keyframes slideInFromLeft {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(0); }
    }
  `;
  document.head.appendChild(style);

  // Add slide animation to sidebar
  sidebar.style.animation = 'slideInFromLeft 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';

  // Close functionality
  function closeSidebar() {
    sidebar.style.animation = 'slideInFromLeft 0.4s ease-in reverse';
    overlay.style.animation = 'fadeIn 0.4s ease-in reverse';

    setTimeout(() => {
      sidebar.remove();
      overlay.remove();
      style.remove();
    }, 400);
  }

  // Close on overlay click
  overlay.addEventListener('click', closeSidebar);

  // Close on Escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      closeSidebar();
    }
  });

  // Append to page
  document.body.appendChild(overlay);
  document.body.appendChild(sidebar);
}

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
  console.log('ViralChef Extension installed:', details.reason);
  
  // Set default settings
  chrome.storage.sync.set({
    language: 'en',
    aiProvider: 'openai',
    searchPreferences: {
      includeFacebook: true,
      includeGoogle: true,
      maxResults: 10
    }
  });
});

// Handle messages from content scripts and popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Background received message:', request);
  
  switch (request.action) {
    case 'openDashboard':
      openDashboard();
      break;
    
    case 'generateRecipe':
      handleRecipeGeneration(request.data, sendResponse);
      return true; // Keep message channel open for async response
    
    case 'searchViralRecipes':
      handleViralSearch(request.data, sendResponse);
      return true; // Keep message channel open for async response
    
    case 'saveRecipe':
      saveRecipe(request.data, sendResponse);
      break;
    
    default:
      console.log('Unknown action:', request.action);
  }
});

// Open dashboard in new tab
function openDashboard() {
  chrome.tabs.create({
    url: chrome.runtime.getURL('dashboard/index.html')
  });
}

// Handle recipe generation requests
async function handleRecipeGeneration(data, sendResponse) {
  try {
    console.log('Generating recipe for:', data.title);
    
    // Get user settings
    const settings = await chrome.storage.sync.get(['aiProvider', 'language']);
    
    // This will be implemented when we create the AI utility
    const recipe = await generateRecipeIngredients(data.title, settings);
    
    sendResponse({ success: true, recipe });
  } catch (error) {
    console.error('Recipe generation failed:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// Handle viral recipe search requests
async function handleViralSearch(data, sendResponse) {
  try {
    console.log('Searching viral recipes for:', data.query);
    
    // Get user settings
    const settings = await chrome.storage.sync.get(['searchPreferences']);
    
    // This will be implemented when we create the search utilities
    const results = await searchViralRecipes(data.query, settings.searchPreferences);
    
    sendResponse({ success: true, results });
  } catch (error) {
    console.error('Viral search failed:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// Save recipe to storage
async function saveRecipe(data, sendResponse) {
  try {
    const { recipes = [] } = await chrome.storage.local.get(['recipes']);
    
    const newRecipe = {
      id: Date.now().toString(),
      title: data.title,
      ingredients: data.ingredients,
      source: data.source || 'generated',
      createdAt: new Date().toISOString(),
      tags: data.tags || []
    };
    
    recipes.push(newRecipe);
    await chrome.storage.local.set({ recipes });
    
    sendResponse({ success: true, recipe: newRecipe });
  } catch (error) {
    console.error('Failed to save recipe:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// Placeholder functions - will be implemented with actual utilities
async function generateRecipeIngredients(title, settings) {
  // This will call the AI utility when implemented
  return {
    title,
    ingredients: ['Placeholder ingredient 1', 'Placeholder ingredient 2'],
    notes: 'Generated by AI'
  };
}

async function searchViralRecipes(query, preferences) {
  // This will call the search utilities when implemented
  return [
    {
      title: 'Sample Viral Recipe',
      source: 'facebook',
      engagement: { likes: 1000, shares: 500 },
      url: 'https://example.com'
    }
  ];
}
