// ViralChef Extension Background Script
console.log('ViralChef Extension Background Script Loaded');

// Handle action button click to open sidebar
chrome.action.onClicked.addListener(async (tab) => {
  console.log('Extension icon clicked, tab:', tab);

  try {
    // Test if we can access the tab
    console.log('Attempting to inject sidebar...');

    // First inject CSS
    await chrome.scripting.insertCSS({
      target: { tabId: tab.id },
      css: `
        @keyframes viralchef-slideInFromLeft {
          0% {
            transform: translateX(-100%);
            opacity: 0;
          }
          100% {
            transform: translateX(0);
            opacity: 1;
          }
        }
        @keyframes viralchef-slideOutToLeft {
          0% {
            transform: translateX(0);
            opacity: 1;
          }
          100% {
            transform: translateX(-100%);
            opacity: 0;
          }
        }
        @keyframes viralchef-fadeIn {
          0% { opacity: 0; }
          100% { opacity: 1; }
        }
        @keyframes viralchef-fadeOut {
          0% { opacity: 1; }
          100% { opacity: 0; }
        }
      `
    });

    console.log('CSS injected successfully');

    // Then inject the sidebar script
    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: createSidebar
    });

    console.log('Sidebar script injected successfully');
  } catch (error) {
    console.error('Failed to inject sidebar:', error);
    console.error('Error details:', error.message);

    // Show alert to user
    try {
      await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: () => {
          alert('ViralChef: Unable to load sidebar on this page. Try refreshing the page or use a different website.');
        }
      });
    } catch (alertError) {
      console.error('Could not show alert:', alertError);
    }
  }
});

// Function to create sidebar (injected into page)
function createSidebar() {
  console.log('createSidebar function called');

  // Remove existing sidebar if any
  const existingSidebar = document.getElementById('viralchef-sidebar');
  const existingOverlay = document.getElementById('viralchef-overlay');

  if (existingSidebar) {
    console.log('Existing sidebar found, removing...');
    // Close existing sidebar with animation
    existingSidebar.style.animation = 'viralchef-slideOutToLeft 0.4s ease-in forwards';
    if (existingOverlay) {
      existingOverlay.style.animation = 'viralchef-fadeOut 0.4s ease-in forwards';
    }
    setTimeout(() => {
      existingSidebar.remove();
      if (existingOverlay) existingOverlay.remove();
      console.log('Existing sidebar removed');
    }, 400);
    return;
  }

  console.log('Creating new sidebar...');

  try {
    // Create overlay first
    const overlay = document.createElement('div');
    overlay.id = 'viralchef-overlay';
    overlay.style.cssText = `
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      background: rgba(0, 0, 0, 0.5) !important;
      backdrop-filter: blur(5px) !important;
      z-index: 2147483646 !important;
      animation: viralchef-fadeIn 0.3s ease-out !important;
    `;
    console.log('Overlay created');

    // Create sidebar container
    const sidebar = document.createElement('div');
    sidebar.id = 'viralchef-sidebar';
    sidebar.style.cssText = `
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 400px !important;
      height: 100vh !important;
      background: linear-gradient(135deg, #1a1a1a, #2d1b1b) !important;
      border-right: 2px solid rgba(220, 38, 38, 0.3) !important;
      box-shadow: 5px 0 25px rgba(0, 0, 0, 0.4) !important;
      z-index: 2147483647 !important;
      animation: viralchef-slideInFromLeft 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
      overflow-y: auto !important;
      padding: 20px !important;
      box-sizing: border-box !important;
      color: #ffffff !important;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    `;
    console.log('Sidebar created');

    // Create simple sidebar content first
    sidebar.innerHTML = \`
    <div style="position: relative;">
      <!-- Close Button -->
      <div id="viralchef-close-btn" style="
        position: absolute;
        top: 15px;
        right: 15px;
        width: 30px;
        height: 30px;
        background: rgba(220, 38, 38, 0.8);
        border: 1px solid rgba(220, 38, 38, 0.4);
        border-radius: 50%;
        color: white;
        font-size: 18px;
        font-weight: bold;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        z-index: 10;
      ">×</div>

      <!-- Header -->
      <div style="text-align: center; margin-bottom: 24px; margin-top: 50px;">
        <div style="
          width: 48px;
          height: 48px;
          margin: 0 auto 12px;
          background: linear-gradient(135deg, #dc2626, #7f1d1d);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 24px;
          font-weight: bold;
          box-shadow: 0 4px 15px rgba(220, 38, 38, 0.4);
          border: 1px solid rgba(220, 38, 38, 0.3);
        ">🍳</div>
        <div style="font-size: 18px; font-weight: 600; margin-bottom: 4px; color: #ffffff;">ViralChef</div>
        <div style="font-size: 12px; color: #9ca3af;">AI Recipe Generator</div>
      </div>

      <!-- Recipe Generator Card -->
      <div style="
        background: rgba(0, 0, 0, 0.4);
        backdrop-filter: blur(15px);
        border-radius: 16px;
        border: 1px solid rgba(220, 38, 38, 0.2);
        padding: 16px;
        margin-bottom: 16px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
      ">
        <div style="margin-bottom: 12px;">
          <label style="display: block; font-size: 12px; font-weight: 500; margin-bottom: 6px; color: #e5e7eb;">Recipe Title</label>
          <input type="text" id="viralchef-recipe-input" placeholder="e.g., Spicy Tuna Pasta" style="
            width: 100%;
            padding: 10px 12px;
            border: 1px solid rgba(220, 38, 38, 0.3);
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(5px);
            font-size: 14px;
            color: #ffffff;
            transition: all 0.2s ease;
            box-sizing: border-box;
          ">
        </div>
        <button id="viralchef-generate-btn" style="
          width: 100%;
          padding: 12px;
          background: rgba(220, 38, 38, 0.8);
          border: 1px solid rgba(220, 38, 38, 0.4);
          border-radius: 12px;
          color: white;
          font-weight: 500;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);
          box-shadow: 0 4px 15px rgba(220, 38, 38, 0.2);
        ">🤖 Generate Ingredients</button>
      </div>

      <!-- Viral Search Card -->
      <div style="
        background: rgba(0, 0, 0, 0.4);
        backdrop-filter: blur(15px);
        border-radius: 16px;
        border: 1px solid rgba(220, 38, 38, 0.2);
        padding: 16px;
        margin-bottom: 16px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
      ">
        <div style="margin-bottom: 12px;">
          <label style="display: block; font-size: 12px; font-weight: 500; margin-bottom: 6px; color: #e5e7eb;">Search Viral Recipes</label>
          <input type="text" id="viralchef-search-input" placeholder="e.g., trending pasta recipes" style="
            width: 100%;
            padding: 10px 12px;
            border: 1px solid rgba(220, 38, 38, 0.3);
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(5px);
            font-size: 14px;
            color: #ffffff;
            transition: all 0.2s ease;
            box-sizing: border-box;
          ">
        </div>
        <button id="viralchef-search-btn" style="
          width: 100%;
          padding: 12px;
          background: rgba(75, 85, 99, 0.8);
          border: 1px solid rgba(75, 85, 99, 0.4);
          border-radius: 12px;
          color: white;
          font-weight: 500;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);
          box-shadow: 0 4px 15px rgba(75, 85, 99, 0.2);
        ">🔥 Find Viral Recipes</button>
      </div>

      <!-- Dashboard Button -->
      <button id="viralchef-dashboard-btn" style="
        width: 100%;
        padding: 12px;
        background: rgba(220, 38, 38, 0.8);
        border: 1px solid rgba(220, 38, 38, 0.4);
        border-radius: 12px;
        color: white;
        font-weight: 500;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        margin-bottom: 16px;
        box-shadow: 0 4px 15px rgba(220, 38, 38, 0.2);
      ">📊 Open Dashboard</button>

      <!-- Quick Actions -->
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-top: 16px;">
        <div style="
          padding: 8px;
          background: rgba(0, 0, 0, 0.3);
          border: 1px solid rgba(220, 38, 38, 0.3);
          border-radius: 8px;
          text-align: center;
          font-size: 11px;
          color: #ffffff;
          cursor: pointer;
          transition: all 0.3s ease;
          backdrop-filter: blur(5px);
        ">📚 Saved<br>Recipes</div>
        <div style="
          padding: 8px;
          background: rgba(0, 0, 0, 0.3);
          border: 1px solid rgba(220, 38, 38, 0.3);
          border-radius: 8px;
          text-align: center;
          font-size: 11px;
          color: #ffffff;
          cursor: pointer;
          transition: all 0.3s ease;
          backdrop-filter: blur(5px);
        ">⚙️ Settings</div>
        <div style="
          padding: 8px;
          background: rgba(0, 0, 0, 0.3);
          border: 1px solid rgba(220, 38, 38, 0.3);
          border-radius: 8px;
          text-align: center;
          font-size: 11px;
          color: #ffffff;
          cursor: pointer;
          transition: all 0.3s ease;
          backdrop-filter: blur(5px);
        ">📈 Trending<br>Now</div>
        <div style="
          padding: 8px;
          background: rgba(0, 0, 0, 0.3);
          border: 1px solid rgba(220, 38, 38, 0.3);
          border-radius: 8px;
          text-align: center;
          font-size: 11px;
          color: #ffffff;
          cursor: pointer;
          transition: all 0.3s ease;
          backdrop-filter: blur(5px);
        ">❤️ Favorites</div>
      </div>

      <!-- Status -->
      <div style="text-align: center; font-size: 12px; color: #9ca3af; margin-top: 16px;">
        Ready to generate amazing recipes!
      </div>
    </div>
  \`;

  // Close functionality
  function closeSidebar() {
    sidebar.style.animation = 'viralchef-slideOutToLeft 0.4s ease-in forwards';
    overlay.style.animation = 'viralchef-fadeOut 0.4s ease-in forwards';

    setTimeout(() => {
      sidebar.remove();
      overlay.remove();
    }, 400);
  }

  // Add event listeners
  const closeBtn = sidebar.querySelector('#viralchef-close-btn');
  const generateBtn = sidebar.querySelector('#viralchef-generate-btn');
  const searchBtn = sidebar.querySelector('#viralchef-search-btn');
  const dashboardBtn = sidebar.querySelector('#viralchef-dashboard-btn');

  closeBtn.addEventListener('click', closeSidebar);
  overlay.addEventListener('click', closeSidebar);

  // Dashboard button
  dashboardBtn.addEventListener('click', () => {
    chrome.runtime.sendMessage({ action: 'openDashboard' });
    closeSidebar();
  });

  // Generate button
  generateBtn.addEventListener('click', () => {
    const input = sidebar.querySelector('#viralchef-recipe-input');
    const title = input.value.trim();
    if (title) {
      chrome.runtime.sendMessage({
        action: 'generateRecipe',
        data: { title }
      });
    } else {
      alert('Please enter a recipe title');
    }
  });

  // Search button
  searchBtn.addEventListener('click', () => {
    const input = sidebar.querySelector('#viralchef-search-input');
    const query = input.value.trim();
    if (query) {
      chrome.runtime.sendMessage({
        action: 'searchViralRecipes',
        data: { query }
      });
    } else {
      alert('Please enter a search query');
    }
  });

  // Close on Escape key
  document.addEventListener('keydown', function escapeHandler(e) {
    if (e.key === 'Escape') {
      closeSidebar();
      document.removeEventListener('keydown', escapeHandler);
    }
  });

  // Add hover effects
  const buttons = sidebar.querySelectorAll('button');
  buttons.forEach(button => {
    button.addEventListener('mouseenter', () => {
      if (button.style.background.includes('220, 38, 38')) {
        button.style.background = 'rgba(220, 38, 38, 0.9)';
        button.style.transform = 'translateY(-2px)';
        button.style.boxShadow = '0 8px 25px rgba(220, 38, 38, 0.3)';
      } else {
        button.style.background = 'rgba(75, 85, 99, 0.9)';
        button.style.transform = 'translateY(-2px)';
        button.style.boxShadow = '0 8px 25px rgba(75, 85, 99, 0.3)';
      }
    });

    button.addEventListener('mouseleave', () => {
      if (button.style.background.includes('220, 38, 38')) {
        button.style.background = 'rgba(220, 38, 38, 0.8)';
      } else {
        button.style.background = 'rgba(75, 85, 99, 0.8)';
      }
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = button.style.background.includes('220, 38, 38') ?
        '0 4px 15px rgba(220, 38, 38, 0.2)' :
        '0 4px 15px rgba(75, 85, 99, 0.2)';
    });
  });

    // Append to page
    document.body.appendChild(overlay);
    document.body.appendChild(sidebar);
    console.log('Sidebar appended to page successfully');

  } catch (error) {
    console.error('Error creating sidebar:', error);
    alert('ViralChef: Error creating sidebar - ' + error.message);
  }
}

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
  console.log('ViralChef Extension installed:', details.reason);
  
  // Set default settings
  chrome.storage.sync.set({
    language: 'en',
    aiProvider: 'openai',
    searchPreferences: {
      includeFacebook: true,
      includeGoogle: true,
      maxResults: 10
    }
  });
});

// Handle messages from content scripts and popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Background received message:', request);
  
  switch (request.action) {
    case 'openDashboard':
      openDashboard();
      break;
    
    case 'generateRecipe':
      handleRecipeGeneration(request.data, sendResponse);
      return true; // Keep message channel open for async response
    
    case 'searchViralRecipes':
      handleViralSearch(request.data, sendResponse);
      return true; // Keep message channel open for async response
    
    case 'saveRecipe':
      saveRecipe(request.data, sendResponse);
      break;
    
    default:
      console.log('Unknown action:', request.action);
  }
});

// Open dashboard in new tab
function openDashboard() {
  chrome.tabs.create({
    url: chrome.runtime.getURL('dashboard/index.html')
  });
}

// Handle recipe generation requests
async function handleRecipeGeneration(data, sendResponse) {
  try {
    console.log('Generating recipe for:', data.title);
    
    // Get user settings
    const settings = await chrome.storage.sync.get(['aiProvider', 'language']);
    
    // This will be implemented when we create the AI utility
    const recipe = await generateRecipeIngredients(data.title, settings);
    
    sendResponse({ success: true, recipe });
  } catch (error) {
    console.error('Recipe generation failed:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// Handle viral recipe search requests
async function handleViralSearch(data, sendResponse) {
  try {
    console.log('Searching viral recipes for:', data.query);
    
    // Get user settings
    const settings = await chrome.storage.sync.get(['searchPreferences']);
    
    // This will be implemented when we create the search utilities
    const results = await searchViralRecipes(data.query, settings.searchPreferences);
    
    sendResponse({ success: true, results });
  } catch (error) {
    console.error('Viral search failed:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// Save recipe to storage
async function saveRecipe(data, sendResponse) {
  try {
    const { recipes = [] } = await chrome.storage.local.get(['recipes']);
    
    const newRecipe = {
      id: Date.now().toString(),
      title: data.title,
      ingredients: data.ingredients,
      source: data.source || 'generated',
      createdAt: new Date().toISOString(),
      tags: data.tags || []
    };
    
    recipes.push(newRecipe);
    await chrome.storage.local.set({ recipes });
    
    sendResponse({ success: true, recipe: newRecipe });
  } catch (error) {
    console.error('Failed to save recipe:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// Placeholder functions - will be implemented with actual utilities
async function generateRecipeIngredients(title, settings) {
  // This will call the AI utility when implemented
  return {
    title,
    ingredients: ['Placeholder ingredient 1', 'Placeholder ingredient 2'],
    notes: 'Generated by AI'
  };
}

async function searchViralRecipes(query, preferences) {
  // This will call the search utilities when implemented
  return [
    {
      title: 'Sample Viral Recipe',
      source: 'facebook',
      engagement: { likes: 1000, shares: 500 },
      url: 'https://example.com'
    }
  ];
}
