{"name": "viralchef-extension", "version": "1.0.0", "description": "AI-powered viral recipe generator Chrome extension", "main": "background.js", "scripts": {"dev": "vite", "build": "npx vite build", "build-with-types": "tsc && vite build", "preview": "vite preview", "build-extension": "npm run build && npm run copy-files", "copy-files": "cp manifest.json dist/ && cp background.js dist/ && cp content.js dist/ && cp popup.html dist/ && cp -r assets dist/", "watch": "vite build --watch"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^13.5.0", "i18next": "^23.7.0", "zustand": "^4.4.7", "lucide-react": "^0.294.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/chrome": "^0.0.251", "@vitejs/plugin-react": "^4.2.1", "typescript": "^5.2.2", "vite": "^5.0.8", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}, "keywords": ["chrome-extension", "recipe-generator", "ai", "viral-recipes", "facebook", "google-search"], "author": "ViralChef Team", "license": "MIT"}