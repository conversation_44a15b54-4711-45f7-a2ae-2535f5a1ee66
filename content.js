// ViralChef Extension Content Script
console.log('ViralChef Content Script Loaded');

// Add CSS animations for slide-in effect
const style = document.createElement('style');
style.textContent = `
  @keyframes slideInFromLeft {
    0% {
      transform: translateX(-100vw);
      opacity: 0;
    }
    70% {
      opacity: 0.8;
    }
    100% {
      transform: translateX(0);
      opacity: 1;
    }
  }
`;
document.head.appendChild(style);

// Detect recipe titles on web pages
function detectRecipeTitles() {
  const selectors = [
    'h1',
    'h2',
    '.recipe-title',
    '.entry-title',
    '[class*="title"]',
    '[class*="recipe"]'
  ];
  
  const potentialTitles = [];
  
  selectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
      const text = element.textContent.trim();
      if (text.length > 10 && text.length < 100 && isLikelyRecipeTitle(text)) {
        potentialTitles.push({
          text,
          element,
          confidence: calculateTitleConfidence(text)
        });
      }
    });
  });
  
  return potentialTitles.sort((a, b) => b.confidence - a.confidence);
}

// Check if text is likely a recipe title
function isLikelyRecipeTitle(text) {
  const recipeKeywords = [
    'recipe', 'cook', 'bake', 'dish', 'meal', 'food',
    'pasta', 'chicken', 'beef', 'fish', 'vegetarian',
    'dessert', 'cake', 'bread', 'soup', 'salad',
    'sauce', 'marinade', 'grilled', 'roasted', 'fried'
  ];
  
  const lowerText = text.toLowerCase();
  return recipeKeywords.some(keyword => lowerText.includes(keyword)) ||
         /\b(with|and|in|for)\b/.test(lowerText);
}

// Calculate confidence score for recipe title
function calculateTitleConfidence(text) {
  let score = 0;
  const lowerText = text.toLowerCase();
  
  // Bonus for recipe-specific words
  if (lowerText.includes('recipe')) score += 30;
  if (lowerText.includes('easy')) score += 10;
  if (lowerText.includes('quick')) score += 10;
  if (lowerText.includes('homemade')) score += 15;
  if (lowerText.includes('best')) score += 10;
  
  // Bonus for food-related words
  const foodWords = ['chicken', 'beef', 'pasta', 'cake', 'bread', 'soup'];
  foodWords.forEach(word => {
    if (lowerText.includes(word)) score += 20;
  });
  
  // Penalty for very long or very short titles
  if (text.length < 20) score -= 10;
  if (text.length > 80) score -= 20;
  
  return Math.max(0, score);
}

// Add floating action button for recipe generation
function addFloatingButton() {
  if (document.getElementById('viralchef-fab')) return;
  
  const fab = document.createElement('div');
  fab.id = 'viralchef-fab';
  fab.innerHTML = `
    <div style="
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, rgba(220, 38, 38, 0.9), rgba(127, 29, 29, 0.9));
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-shadow: 0 4px 20px rgba(220, 38, 38, 0.3);
      backdrop-filter: blur(15px);
      border: 1px solid rgba(220, 38, 38, 0.4);
      z-index: 10000;
      transition: all 0.3s ease;
      animation: slideInFromLeft 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    " onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1)'">
      <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
      </svg>
    </div>
  `;
  
  fab.addEventListener('click', () => {
    chrome.runtime.sendMessage({ action: 'openDashboard' });
  });
  
  document.body.appendChild(fab);
}

// Add context menu for detected recipe titles
function addContextMenus() {
  const titles = detectRecipeTitles();
  
  titles.slice(0, 3).forEach((titleData, index) => {
    const element = titleData.element;
    
    // Add hover effect
    element.addEventListener('mouseenter', () => {
      element.style.backgroundColor = 'rgba(220, 38, 38, 0.1)';
      element.style.cursor = 'pointer';
      element.style.borderLeft = '3px solid #dc2626';

      // Show tooltip
      showTooltip(element, 'Click to generate ingredients with ViralChef');
    });
    
    element.addEventListener('mouseleave', () => {
      element.style.backgroundColor = '';
      element.style.cursor = '';
      element.style.borderLeft = '';
      hideTooltip();
    });
    
    // Add click handler
    element.addEventListener('click', (e) => {
      e.preventDefault();
      generateRecipeFromTitle(titleData.text);
    });
  });
}

// Show tooltip
function showTooltip(element, text) {
  hideTooltip(); // Remove any existing tooltip
  
  const tooltip = document.createElement('div');
  tooltip.id = 'viralchef-tooltip';
  tooltip.textContent = text;
  tooltip.style.cssText = `
    position: absolute;
    background: rgba(26, 26, 26, 0.95);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 12px;
    z-index: 10001;
    pointer-events: none;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(220, 38, 38, 0.3);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  `;
  
  const rect = element.getBoundingClientRect();
  tooltip.style.top = (rect.top - 40 + window.scrollY) + 'px';
  tooltip.style.left = (rect.left + window.scrollX) + 'px';
  
  document.body.appendChild(tooltip);
}

// Hide tooltip
function hideTooltip() {
  const tooltip = document.getElementById('viralchef-tooltip');
  if (tooltip) tooltip.remove();
}

// Generate recipe from detected title
function generateRecipeFromTitle(title) {
  chrome.runtime.sendMessage({
    action: 'generateRecipe',
    data: { title }
  }, (response) => {
    if (response.success) {
      showRecipeModal(response.recipe);
    } else {
      console.error('Recipe generation failed:', response.error);
    }
  });
}

// Show recipe modal
function showRecipeModal(recipe) {
  // Remove existing modal
  const existingModal = document.getElementById('viralchef-modal');
  if (existingModal) existingModal.remove();
  
  const modal = document.createElement('div');
  modal.id = 'viralchef-modal';
  modal.innerHTML = `
    <div style="
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.7);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10002;
      backdrop-filter: blur(10px);
      animation: slideInFromLeft 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    ">
      <div style="
        background: linear-gradient(135deg, rgba(26, 26, 26, 0.95), rgba(45, 27, 27, 0.95));
        border-radius: 20px;
        padding: 24px;
        max-width: 500px;
        width: 90%;
        max-height: 80%;
        overflow-y: auto;
        backdrop-filter: blur(25px);
        border: 1px solid rgba(220, 38, 38, 0.3);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
        color: #ffffff;
      ">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
          <h3 style="margin: 0; color: #ffffff; font-size: 18px; font-weight: 600;">${recipe.title}</h3>
          <button id="close-modal" style="
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #9ca3af;
          ">&times;</button>
        </div>
        <div style="margin-bottom: 16px;">
          <h4 style="color: #e5e7eb; margin-bottom: 8px;">Ingredients:</h4>
          <ul style="list-style: none; padding: 0;">
            ${recipe.ingredients.map(ingredient => `
              <li style="
                padding: 8px;
                margin: 4px 0;
                background: rgba(220, 38, 38, 0.2);
                border-radius: 8px;
                border-left: 3px solid #dc2626;
              ">${ingredient}</li>
            `).join('')}
          </ul>
        </div>
        <div style="display: flex; gap: 12px; justify-content: flex-end;">
          <button id="save-recipe" style="
            background: rgba(220, 38, 38, 0.8);
            color: white;
            border: 1px solid rgba(220, 38, 38, 0.4);
            padding: 10px 20px;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 500;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
          ">Save Recipe</button>
          <button id="open-dashboard" style="
            background: rgba(75, 85, 99, 0.8);
            color: white;
            border: 1px solid rgba(75, 85, 99, 0.4);
            padding: 10px 20px;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 500;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
          ">Open Dashboard</button>
        </div>
      </div>
    </div>
  `;
  
  document.body.appendChild(modal);
  
  // Add event listeners
  document.getElementById('close-modal').addEventListener('click', () => modal.remove());
  document.getElementById('save-recipe').addEventListener('click', () => {
    chrome.runtime.sendMessage({
      action: 'saveRecipe',
      data: recipe
    });
    modal.remove();
  });
  document.getElementById('open-dashboard').addEventListener('click', () => {
    chrome.runtime.sendMessage({ action: 'openDashboard' });
    modal.remove();
  });
  
  // Close on backdrop click
  modal.addEventListener('click', (e) => {
    if (e.target === modal) modal.remove();
  });
}

// Initialize content script
function init() {
  // Only run on pages that might contain recipes
  const url = window.location.href;
  const recipeKeywords = ['recipe', 'food', 'cooking', 'kitchen', 'chef', 'meal'];
  
  if (recipeKeywords.some(keyword => url.toLowerCase().includes(keyword)) ||
      document.title.toLowerCase().includes('recipe')) {
    
    addFloatingButton();
    
    // Wait for page to load completely
    setTimeout(() => {
      addContextMenus();
    }, 2000);
  }
}

// Run initialization
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', init);
} else {
  init();
}
