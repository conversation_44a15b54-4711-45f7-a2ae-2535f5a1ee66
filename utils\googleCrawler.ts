// Google Search Crawler for Viral Recipe Discovery
export interface GoogleSearchResult {
  title: string
  url: string
  snippet: string
  displayUrl: string
  rank: number
  recipeData?: RecipeStructuredData
  engagement?: {
    estimatedViews: number
    socialShares: number
  }
  source: string
  publishDate?: string
  confidence: number
}

export interface RecipeStructuredData {
  name: string
  description: string
  ingredients: string[]
  instructions: string[]
  cookTime?: string
  prepTime?: string
  totalTime?: string
  servings?: number
  calories?: number
  rating?: {
    value: number
    count: number
  }
  author?: string
  image?: string
}

export interface GoogleSearchOptions {
  query: string
  maxResults?: number
  dateRange?: 'day' | 'week' | 'month' | 'year'
  site?: string
  language?: string
  region?: string
}

// Google Custom Search API integration
export class GoogleCustomSearch {
  private apiKey: string
  private searchEngineId: string
  private baseUrl = 'https://www.googleapis.com/customsearch/v1'

  constructor(apiKey: string, searchEngineId: string) {
    this.apiKey = apiKey
    this.searchEngineId = searchEngineId
  }

  async searchRecipes(options: GoogleSearchOptions): Promise<GoogleSearchResult[]> {
    try {
      const params = new URLSearchParams({
        key: this.apiKey,
        cx: this.searchEngineId,
        q: this.buildSearchQuery(options.query),
        num: Math.min(options.maxResults || 10, 10).toString(),
        safe: 'active'
      })

      if (options.dateRange) {
        params.append('dateRestrict', this.getDateRestrict(options.dateRange))
      }

      if (options.site) {
        params.set('q', `${params.get('q')} site:${options.site}`)
      }

      if (options.language) {
        params.append('lr', `lang_${options.language}`)
      }

      if (options.region) {
        params.append('gl', options.region)
      }

      const response = await fetch(`${this.baseUrl}?${params}`)
      
      if (!response.ok) {
        throw new Error(`Google Search API error: ${response.status}`)
      }

      const data = await response.json()
      
      if (!data.items) {
        return []
      }

      const results = await Promise.all(
        data.items.map(async (item: any, index: number) => {
          const result = await this.parseSearchResult(item, index + 1)
          return result
        })
      )

      return results
        .filter(result => result.confidence > 30)
        .sort((a, b) => b.confidence - a.confidence)
    } catch (error) {
      console.error('Google search failed:', error)
      return []
    }
  }

  private buildSearchQuery(query: string): string {
    // Enhance the query with recipe-specific terms
    const recipeTerms = ['recipe', 'cooking', 'ingredients', 'how to make']
    const hasRecipeTerm = recipeTerms.some(term => 
      query.toLowerCase().includes(term)
    )

    if (!hasRecipeTerm) {
      return `${query} recipe`
    }

    return query
  }

  private getDateRestrict(range: string): string {
    switch (range) {
      case 'day': return 'd1'
      case 'week': return 'w1'
      case 'month': return 'm1'
      case 'year': return 'y1'
      default: return 'm1'
    }
  }

  private async parseSearchResult(item: any, rank: number): Promise<GoogleSearchResult> {
    const confidence = this.calculateConfidence(item, rank)
    
    const result: GoogleSearchResult = {
      title: item.title,
      url: item.link,
      snippet: item.snippet,
      displayUrl: item.displayLink,
      rank,
      source: this.extractSource(item.displayLink),
      confidence
    }

    // Try to extract structured data
    if (item.pagemap?.recipe) {
      result.recipeData = this.parseRecipeStructuredData(item.pagemap.recipe[0])
    }

    // Estimate engagement based on source and rank
    result.engagement = this.estimateEngagement(result.source, rank, confidence)

    return result
  }

  private calculateConfidence(item: any, rank: number): number {
    let score = 100 - (rank * 5) // Base score decreases with rank

    const title = item.title.toLowerCase()
    const snippet = item.snippet.toLowerCase()
    const content = `${title} ${snippet}`

    // Content-based scoring
    if (content.includes('recipe')) score += 20
    if (content.includes('ingredients')) score += 15
    if (content.includes('easy') || content.includes('quick')) score += 10
    if (content.includes('best') || content.includes('perfect')) score += 10
    if (content.includes('homemade')) score += 8
    if (content.includes('delicious') || content.includes('amazing')) score += 5

    // Source-based scoring
    const source = item.displayLink.toLowerCase()
    if (source.includes('allrecipes')) score += 15
    if (source.includes('foodnetwork')) score += 15
    if (source.includes('epicurious')) score += 12
    if (source.includes('tasty')) score += 12
    if (source.includes('youtube')) score += 10
    if (source.includes('pinterest')) score += 8

    // Structured data bonus
    if (item.pagemap?.recipe) score += 20

    return Math.max(0, Math.min(100, score))
  }

  private parseRecipeStructuredData(recipe: any): RecipeStructuredData {
    return {
      name: recipe.name || '',
      description: recipe.description || '',
      ingredients: Array.isArray(recipe.recipeingredient) 
        ? recipe.recipeingredient 
        : recipe.recipeingredient ? [recipe.recipeingredient] : [],
      instructions: Array.isArray(recipe.recipeinstructions)
        ? recipe.recipeinstructions
        : recipe.recipeinstructions ? [recipe.recipeinstructions] : [],
      cookTime: recipe.cooktime,
      prepTime: recipe.preptime,
      totalTime: recipe.totaltime,
      servings: recipe.recipeyield ? parseInt(recipe.recipeyield) : undefined,
      calories: recipe.nutrition?.calories ? parseInt(recipe.nutrition.calories) : undefined,
      rating: recipe.aggregaterating ? {
        value: parseFloat(recipe.aggregaterating.ratingvalue || '0'),
        count: parseInt(recipe.aggregaterating.ratingcount || '0')
      } : undefined,
      author: recipe.author?.name || recipe.author,
      image: recipe.image
    }
  }

  private extractSource(displayLink: string): string {
    const domain = displayLink.toLowerCase()
    
    if (domain.includes('allrecipes')) return 'AllRecipes'
    if (domain.includes('foodnetwork')) return 'Food Network'
    if (domain.includes('epicurious')) return 'Epicurious'
    if (domain.includes('tasty')) return 'Tasty'
    if (domain.includes('youtube')) return 'YouTube'
    if (domain.includes('pinterest')) return 'Pinterest'
    if (domain.includes('delish')) return 'Delish'
    if (domain.includes('foodandwine')) return 'Food & Wine'
    if (domain.includes('bonappetit')) return 'Bon Appétit'
    
    return displayLink
  }

  private estimateEngagement(source: string, rank: number, confidence: number): {
    estimatedViews: number
    socialShares: number
  } {
    // Base engagement estimation
    let baseViews = 10000 - (rank * 1000)
    let baseShares = 100 - (rank * 10)

    // Source multipliers
    const sourceMultipliers: { [key: string]: number } = {
      'YouTube': 5,
      'Tasty': 4,
      'Food Network': 3,
      'AllRecipes': 3,
      'Pinterest': 2.5,
      'Epicurious': 2
    }

    const multiplier = sourceMultipliers[source] || 1
    
    return {
      estimatedViews: Math.max(1000, Math.round(baseViews * multiplier * (confidence / 100))),
      socialShares: Math.max(10, Math.round(baseShares * multiplier * (confidence / 100)))
    }
  }
}

// Alternative web scraping approach
export class GoogleWebScraper {

  async searchRecipes(options: GoogleSearchOptions): Promise<GoogleSearchResult[]> {
    // Note: This would require a proper scraping setup
    // For now, return mock data
    return this.getMockResults(options)
  }

  private getMockResults(options: GoogleSearchOptions): GoogleSearchResult[] {
    return [
      {
        title: 'Best Homemade Pasta Recipe - Easy & Delicious',
        url: 'https://example.com/pasta-recipe',
        snippet: 'Learn how to make the perfect homemade pasta with this easy recipe. Simple ingredients, amazing results!',
        displayUrl: 'example.com',
        rank: 1,
        source: 'Recipe Blog',
        confidence: 85,
        engagement: {
          estimatedViews: 25000,
          socialShares: 450
        },
        recipeData: {
          name: 'Homemade Pasta',
          description: 'Easy homemade pasta recipe',
          ingredients: ['2 cups flour', '3 eggs', '1 tsp salt'],
          instructions: ['Mix ingredients', 'Knead dough', 'Roll and cut'],
          prepTime: '30 minutes',
          cookTime: '10 minutes',
          servings: 4
        }
      }
    ]
  }
}

// Main Google search service
export class GoogleRecipeSearch {
  private customSearch?: GoogleCustomSearch
  private webScraper: GoogleWebScraper

  constructor(apiKey?: string, searchEngineId?: string) {
    if (apiKey && searchEngineId) {
      this.customSearch = new GoogleCustomSearch(apiKey, searchEngineId)
    }
    this.webScraper = new GoogleWebScraper()
  }

  async searchViralRecipes(options: GoogleSearchOptions): Promise<GoogleSearchResult[]> {
    try {
      // Try Custom Search API first
      if (this.customSearch) {
        const results = await this.customSearch.searchRecipes(options)
        if (results.length > 0) {
          return results
        }
      }

      // Fallback to web scraping
      return await this.webScraper.searchRecipes(options)
    } catch (error) {
      console.error('Google recipe search failed:', error)
      return []
    }
  }

  async getTrendingRecipes(timeframe: 'day' | 'week' | 'month' = 'week'): Promise<GoogleSearchResult[]> {
    const trendingQueries = [
      'viral recipe trending',
      'popular recipe this week',
      'trending food recipe',
      'viral cooking video',
      'most shared recipe'
    ]

    const allResults: GoogleSearchResult[] = []

    for (const query of trendingQueries) {
      const results = await this.searchViralRecipes({
        query,
        maxResults: 5,
        dateRange: timeframe
      })
      allResults.push(...results)
    }

    // Remove duplicates and sort by confidence
    const uniqueResults = allResults.filter((result, index, self) =>
      index === self.findIndex(r => r.url === result.url)
    )

    return uniqueResults
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 20)
  }
}

// Utility functions
export async function getGoogleSearchCredentials(): Promise<{
  apiKey: string | null
  searchEngineId: string | null
}> {
  const settings = await chrome.storage.sync.get(['googleApiKey', 'googleSearchEngineId'])
  return {
    apiKey: settings.googleApiKey || null,
    searchEngineId: settings.googleSearchEngineId || null
  }
}

export async function searchGoogleRecipes(query: string, options?: Partial<GoogleSearchOptions>): Promise<GoogleSearchResult[]> {
  const credentials = await getGoogleSearchCredentials()
  const searchService = new GoogleRecipeSearch(
    credentials.apiKey || undefined,
    credentials.searchEngineId || undefined
  )
  
  const searchOptions: GoogleSearchOptions = {
    query,
    maxResults: 10,
    dateRange: 'month',
    language: 'en',
    ...options
  }
  
  return await searchService.searchViralRecipes(searchOptions)
}

export function isValidRecipeUrl(url: string): boolean {
  const recipeKeywords = [
    'recipe', 'cooking', 'food', 'kitchen', 'chef',
    'allrecipes', 'foodnetwork', 'epicurious', 'tasty'
  ]
  
  const lowerUrl = url.toLowerCase()
  return recipeKeywords.some(keyword => lowerUrl.includes(keyword))
}

export function extractDomainFromUrl(url: string): string {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname.replace('www.', '')
  } catch {
    return url
  }
}
