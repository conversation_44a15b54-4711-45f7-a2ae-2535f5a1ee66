// ViralChef Popup Script
document.addEventListener('DOMContentLoaded', function() {
  const generateBtn = document.getElementById('generate-btn');
  const searchBtn = document.getElementById('search-btn');
  const dashboardBtn = document.getElementById('dashboard-btn');
  const recipeTitleInput = document.getElementById('recipe-title');
  const searchQueryInput = document.getElementById('search-query');
  const loading = document.getElementById('loading');
  const status = document.getElementById('status');
  
  // Quick action buttons
  const savedRecipesBtn = document.getElementById('saved-recipes');
  const settingsBtn = document.getElementById('settings');
  const trendingBtn = document.getElementById('trending');
  const favoritesBtn = document.getElementById('favorites');
  
  // Generate recipe ingredients
  generateBtn.addEventListener('click', async () => {
    const title = recipeTitleInput.value.trim();
    if (!title) {
      showStatus('Please enter a recipe title', 'error');
      return;
    }
    
    showLoading(true);
    showStatus('Generating ingredients with AI...');
    
    try {
      const response = await sendMessage({
        action: 'generateRecipe',
        data: { title }
      });
      
      if (response.success) {
        showStatus('Recipe generated successfully!', 'success');
        // Open dashboard to show results
        setTimeout(() => {
          chrome.runtime.sendMessage({ action: 'openDashboard' });
          window.close();
        }, 1000);
      } else {
        showStatus('Failed to generate recipe: ' + response.error, 'error');
      }
    } catch (error) {
      showStatus('Error: ' + error.message, 'error');
    } finally {
      showLoading(false);
    }
  });
  
  // Search viral recipes
  searchBtn.addEventListener('click', async () => {
    const query = searchQueryInput.value.trim();
    if (!query) {
      showStatus('Please enter a search query', 'error');
      return;
    }
    
    showLoading(true);
    showStatus('Searching for viral recipes...');
    
    try {
      const response = await sendMessage({
        action: 'searchViralRecipes',
        data: { query }
      });
      
      if (response.success) {
        showStatus(`Found ${response.results.length} viral recipes!`, 'success');
        // Open dashboard to show results
        setTimeout(() => {
          chrome.runtime.sendMessage({ action: 'openDashboard' });
          window.close();
        }, 1000);
      } else {
        showStatus('Search failed: ' + response.error, 'error');
      }
    } catch (error) {
      showStatus('Error: ' + error.message, 'error');
    } finally {
      showLoading(false);
    }
  });
  
  // Open dashboard
  dashboardBtn.addEventListener('click', () => {
    chrome.runtime.sendMessage({ action: 'openDashboard' });
    window.close();
  });
  
  // Quick actions
  savedRecipesBtn.addEventListener('click', () => {
    chrome.runtime.sendMessage({ 
      action: 'openDashboard',
      data: { tab: 'saved' }
    });
    window.close();
  });
  
  settingsBtn.addEventListener('click', () => {
    chrome.runtime.sendMessage({ 
      action: 'openDashboard',
      data: { tab: 'settings' }
    });
    window.close();
  });
  
  trendingBtn.addEventListener('click', () => {
    chrome.runtime.sendMessage({ 
      action: 'openDashboard',
      data: { tab: 'trending' }
    });
    window.close();
  });
  
  favoritesBtn.addEventListener('click', () => {
    chrome.runtime.sendMessage({ 
      action: 'openDashboard',
      data: { tab: 'favorites' }
    });
    window.close();
  });
  
  // Enter key handlers
  recipeTitleInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
      generateBtn.click();
    }
  });
  
  searchQueryInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
      searchBtn.click();
    }
  });
  
  // Helper functions
  function showLoading(show) {
    loading.style.display = show ? 'block' : 'none';
    generateBtn.disabled = show;
    searchBtn.disabled = show;
  }
  
  function showStatus(message, type = 'info') {
    status.textContent = message;
    status.style.color = type === 'error' ? '#ef4444' : 
                        type === 'success' ? '#10b981' : '#6b7280';
    
    // Clear status after 3 seconds for success/error messages
    if (type !== 'info') {
      setTimeout(() => {
        status.textContent = 'Ready to generate amazing recipes!';
        status.style.color = '#6b7280';
      }, 3000);
    }
  }
  
  function sendMessage(message) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });
  }
  
  // Load saved data on popup open
  loadPopupData();
  
  async function loadPopupData() {
    try {
      // Get recent searches and recipes
      const data = await chrome.storage.local.get(['recentSearches', 'recentRecipes']);
      
      // Auto-fill last search if available
      if (data.recentSearches && data.recentSearches.length > 0) {
        const lastSearch = data.recentSearches[0];
        if (lastSearch.type === 'recipe') {
          recipeTitleInput.placeholder = `e.g., ${lastSearch.query}`;
        } else if (lastSearch.type === 'viral') {
          searchQueryInput.placeholder = `e.g., ${lastSearch.query}`;
        }
      }
      
      // Update status with stats
      if (data.recentRecipes && data.recentRecipes.length > 0) {
        showStatus(`${data.recentRecipes.length} recipes saved`);
      }
    } catch (error) {
      console.error('Failed to load popup data:', error);
    }
  }
});
